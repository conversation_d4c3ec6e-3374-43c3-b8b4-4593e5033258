<?php

namespace Database\Seeders;

use App\Models\Domain;
use App\Models\Ip;
use App\Models\RegisteredDomain;
use App\Modules\Contact\Constants\ContactType;
use App\Modules\CustomLogger\Services\AuthLogger;
use App\Modules\Domain\Constants\DomainContact;
use App\Modules\Domain\Constants\DomainStatus;
use App\Modules\Domain\Constants\UserDomainStatus;
use App\Modules\Epp\Constants\EppDomainStatus;
use App\Traits\UserContact;
use App\Util\Helper\Client\ClientIp;
use App\Util\Helper\Domain\DefaultDomains;
use App\Util\Helper\Domain\DomainParser;
use App\Util\Helper\Domain\DomainTld;
use App\Util\Helper\Store\BulkActions;
use Carbon\Carbon;
use Exception;
use Illuminate\Database\Seeder;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class DomainListSeeder extends Seeder
{
    use UserContact;

    private $csvFile = 'test.csv'; // domains only

    private $userId = 7;

    private $domainStatus = DomainStatus::ACTIVE;

    private $registeredDomainStatus = UserDomainStatus::OWNED;

    private $expiry = null;

    public function run(): void
    {
        // if (! $this->checkIp() && strcmp(env('APP_ENV'), 'local') != 0) {
        //     echo 'DomainListSeeder::class ->Seeder has been disabled.' . PHP_EOL;

        //     return;
        // }

        $this->expiry = now()->valueOf();
        $this->create();
    }

    private function checkIp(): bool
    {
        $request = app('request');

        $clientIp = ClientIp::getClientIp($request);
        $ipActive = Ip::where('ip', $clientIp)->value('is_active');

        echo 'DomainListSeeder::class -> Checking IP: ' . $clientIp . ' - ' . $ipActive . PHP_EOL;
        app(AuthLogger::class)->info('DomainListSeeder: ' . $clientIp . ' - ' . $ipActive);

        return boolval($ipActive);
    }

    private function create(): void
    {
        try {
            $domains = $this->getCSVData();

            $contacts = $this->getDefaultContacts($this->userId);
            $extIds = DomainTld::getTldsByExtension();
            $categoryId = $this->getCategory($this->userId);

            // insert domains
            $domainData = $this->createDomainData($domains, $contacts, $extIds);
            $createdDomains = $this->insertDomainsToDB($domainData);

            // insert registered domains
            $registeredDomainPayload = $this->createRegisteredDomainPayload($categoryId, $contacts, $createdDomains);
            $this->insertRegisteredDomainsToDB($registeredDomainPayload);
        } catch (Exception $e) {
            echo '' . $e->getMessage();
            app(AuthLogger::class)->error('DomainListSeeder: ' . $e->getMessage());
        }
    }

    private function getCSVData(): array
    {
        $csv = __DIR__ . '/' . $this->csvFile;
        $data = array_map('str_getcsv', file($csv));

        return $data;
    }

    private function createDomainData(array $domains, array $contacts, array $extIds)
    {
        $domains = $this->getCSVData();
        $domainData = [];

        for ($i = 0; $i < count($domains); $i++) {
            $domainItem = $domains[$i];
            print_r($domainItem);
            $domainData[] = $this->createDomainPayload($contacts, $extIds, $domainItem);

            echo 'Domain:: ' . $domainItem[0] . ' - ' . $i . ' of ' . count($domains) . PHP_EOL;
        }

        return $domainData;
    }

    private function getNameServers(array $domain): array
    {
        $nameservers = [];
        for ($i = 1; $i < 5; $i++) {
            if (trim($domain[$i]) != '') {
                $nameservers[] = $domain[$i];
            }
        }

        return $nameservers;
    }

    private function createDomainPayload(array $contacts, array $extIds, array $domainItem): array
    {
        $name = $domainItem[0];
        $now = Carbon::now();

        $registryName = DomainParser::getNameAndExtension($name);
        $extension = $registryName['extension'];
        $registryId = $extIds[$extension]->registry_id;
        $statusArray = EppDomainStatus::CLIENT_LOCK_STATUS;
        $contactsArray = DefaultDomains::getDefaultContactsArray($contacts[$registryId], 'name');

        $domain = [
            'name' => strtolower($name),
            'status' => $this->domainStatus,
            'root' => $extIds[$extension]->id,
            'registrant' => $contacts[$registryId]['name'][DomainContact::REGISTRANT],
            'year_length' => 1,
            'expiry' => $this->expiry,
            'transferredIn' => null,
            'transferredOut' => null,
            'auth_code_updated_at' => $now,
            'contacts' => json_encode($contactsArray),
            'client_status' => json_encode($statusArray),
            'hosts' => null,
            'nameservers' => null,
            'server_renew_at' => $now,
            'client_renew_at' => $now,
            'created_at' => $now,
            'updated_at' => $now,

        ];

        return $domain;
    }

    private function insertDomainsToDB(array $domainData): Collection
    {
        $table = (new Domain)->getTable();
        $domainsCreated = BulkActions::instance()->bulkInsertGetData($table, $domainData);
        echo 'Domains inserted:: ' . count($domainsCreated) . PHP_EOL;

        return $domainsCreated;
    }

    private function createRegisteredDomainPayload(int $categoryId, array $contacts, Collection $insertedDomains): Collection
    {
        $status = $this->registeredDomainStatus;
        $tlds = DomainTld::getAllTlds();

        return $insertedDomains->map(function ($e) use ($tlds, $contacts, $categoryId, $status) {
            $registryId = $tlds[$e->root]['registry_id'];
            $extensionId = $tlds[$e->root]['extension_id'];

            return [
                'user_contact_registrar_id' => $contacts[$registryId]['id'][DomainContact::REGISTRANT],
                'extension_id' => $extensionId,
                'domain_id' => $e->id,
                'status' => $status,
                'locked_until' => Carbon::now()->addDays(1)->timestamp,
                'user_category_id' => $categoryId,
                'contacts_id' => json_encode($contacts[$registryId]['id']),
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ];
        });
    }

    private function insertRegisteredDomainsToDB(Collection $unregisteredDomains): Collection
    {
        $table = (new RegisteredDomain)->getTable();
        $domainsCreated = BulkActions::instance()->bulkInsertGetData($table, $unregisteredDomains->toArray());
        echo 'Registered domains inserted:: ' . count($domainsCreated) . PHP_EOL;

        return $domainsCreated;
    }

    private static function getCategory(int $userId): ?int
    {
        return DB::table('user_categories')
            ->where('user_id', $userId)
            ->where('is_default', true)
            ->value('id');
    }
}