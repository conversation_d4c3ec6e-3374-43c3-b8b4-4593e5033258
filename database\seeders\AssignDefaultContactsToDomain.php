<?php

namespace Database\Seeders;

use App\Models\Domain;
use App\Models\RegisteredDomain;
use App\Modules\CustomLogger\Services\AuthLogger;
use App\Modules\Domain\Constants\DomainContact;
use App\Modules\Domain\Constants\DomainStatus;
use App\Modules\Domain\Services\EppDomainService;
use App\Modules\Epp\Constants\EppDomainStatus;
use App\Modules\Setting\Constants\SettingKey;
use App\Modules\Setting\Services\Settings;
use App\Util\Helper\Domain\DomainParser;
use Carbon\Carbon;
use Exception;
use Illuminate\Auth\Events\Registered;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class AssignDefaultContactsToDomain extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        if (strcmp(env('APP_ENV'), 'local') != 0) {
            echo 'DatabaseSeeder::class ->Seeder has been disabled.' . PHP_EOL;

            return;
        }

        // 1] run this first, comment 2]
        $domains = $this->getAllDomains();
        $this->checkEppInfo($domains); // domain contacts, status

        // comment 1], run this 2]
        // $domains = $this->getAllDomains();
        // $this->updateRegDomainContacts($domains); // domain contacts id - local
    }

    private function getAllDomains()
    {

        return DB::table('domains')
            ->join('registered_domains', 'registered_domains.domain_id', '=', 'domains.id')
            ->join('user_contacts', 'user_contacts.id', '=', 'registered_domains.user_contact_registrar_id')
            ->whereNull('domains.deleted_at')
            // ->where('domains.status', '!=', DomainStatus::ACTIVE)
            ->where('domains.id', '>=', 11)
            ->where('domains.id', '<=', 110)
            ->select(
                'domains.id as id',
                'domains.id as domain_id',
                'domains.name',
                'domains.root',
                'domains.created_at',
                'domains.updated_at',
                'domains.registrant',
                'registered_domains.id as registered_domain_id',
                'registered_domains.user_contact_registrar_id',
                'user_contacts.user_id as user_id',
            )
            ->orderBy('domains.id', 'asc')
            ->get()->all();
    }

    private function updateRegDomainContacts($domains): void
    {
        $lockinPeriod = intval(Settings::instance()->getValueByKey(SettingKey::DOMAIN_LOCKIN_PERIOD));

        foreach ($domains as $d) {
            $created_at = $d->created_at;
            $locked_date = Carbon::createFromFormat('Y-m-d H:i:s', $created_at)->addDays($lockinPeriod)->timestamp;

            $registeredDomain = [
                'id' => $d->registered_domain_id,
                'name' => $d->name,
                'user_contact_registrar_id' => $d->user_contact_registrar_id,
                'contacts_id' => [
                    DomainContact::REGISTRANT => $d->user_contact_registrar_id,
                    DomainContact::ADMIN => $d->user_contact_registrar_id,
                    DomainContact::TECH => $d->user_contact_registrar_id,
                    DomainContact::BILLING => $d->user_contact_registrar_id,
                ],
                'locked_until' => $locked_date,
                'created_at' => $d->created_at,
                'updated_at' => $d->updated_at,
            ];

            $this->updateRegisteredDomain($registeredDomain);
        }
    }

    private function updateRegisteredDomain($domain)
    {
        // update registered domains
        $id = $domain['id'];
        unset($domain['id']);
        $name = $domain['name'];
        unset($domain['name']);

        RegisteredDomain::when(is_int($id), function ($query) use ($id) {
            return $query->where('id', $id);
        })->update($domain);

        $domain['name'] = $name;

        print_r('Updated RegisteredDomain: ' . $name . PHP_EOL);
    }

    private function checkEppInfo($domains): void
    {
        $count = 0;
        foreach ($domains as $domain) {
            $domainName = $domain->name;

            print_r($count++ . ': ');
            try {
                $eppDomainInfo = EppDomainService::instance()->callEppDomainInfo($domainName);
                $eppData = $eppDomainInfo['data'];
                // $eppDomainStatus = $eppDomainInfo['status'];

                $newDomain = [
                    'id' => $domain->id,
                    'name' => $domain->name,
                    'registrant' => $eppData[DomainContact::REGISTRANT],
                    'status' => DomainStatus::ACTIVE,
                    'expiry' => Carbon::parse($eppData['expiry'])->valueOf(),
                    'created_at' => Carbon::parse($eppData['created']),
                    'updated_at' => Carbon::parse($eppData['updated']),
                    'contacts' => json_encode($eppData['contacts']),
                    'client_status' => json_encode($eppData['status']),
                    'nameservers' => json_encode($eppData['nameservers']),
                ];

                $this->updateDomain($newDomain);
            } catch (Exception $e) {
                print_r($e->getMessage() . PHP_EOL);
                app(AuthLogger::class)->error($e->getMessage());

                $newDomain = [
                    'id' => $domain->id,
                    'name' => $domain->name,
                    'registrant' => $domain->registrant,
                    'status' => DomainStatus::FAILED,
                    'contacts' => [
                        DomainContact::REGISTRANT => $domain->registrant,
                        DomainContact::ADMIN => $domain->registrant,
                        DomainContact::TECH => $domain->registrant,
                        DomainContact::BILLING => $domain->registrant,
                    ],
                    'client_status' => json_encode(EppDomainStatus::CLIENT_STATUS),
                ];

                $this->updateDomain($newDomain);
            }
        }
    }

    private function updateDomain($domain): void
    {
        $id = $domain['id'];
        unset($domain['id']);
        $name = $domain['name'];
        unset($domain['name']);

        // $status = [
        //     'status' => $domain['status'],
        //     'created_at' => $domain['created_at'],
        //     'updated_at' => $domain['updated_at'],
        // ];

        $updatePayload = $domain;
        // $updatePayload = $status;

        Domain::when(is_int($id), function ($query) use ($id) {
            return $query->where('id', $id);
        })->update($updatePayload);

        print_r('Updated domain ' . $name . ' with status ' . $domain['status'] . PHP_EOL);
        app(AuthLogger::class)->info('Updated domain ' . $name . ' with values ' . json_encode($domain));
    }
}
